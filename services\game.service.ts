import { apiClient } from './api.service';
import {
  GameSearchParams,
  GameSearchResponse,
  GameUrlResponse,
  GameHistoryParams,
  CategoriesResponse,
  TitlesResponse,
  UseGameSearchParams
} from '@/types/game.types';

class GameService {
  private readonly baseUrl = '/v1/games';

  /**
   * Search games with filters and pagination
   */
  async searchGames(params: UseGameSearchParams): Promise<GameSearchResponse> {
    const queryParams = new URLSearchParams();

    if (params.name) queryParams.set('name', params.name);
    if (params.categories?.length) queryParams.set('categories', params.categories.join(','));
    if (params.device) queryParams.set('device', params.device);
    if (params.page) queryParams.set('page', params.page.toString());
    if (params.limit) queryParams.set('limit', params.limit.toString());
    if (params.sortBy) queryParams.set('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.set('sortOrder', params.sortOrder);

    const response = await apiClient.get<GameSearchResponse>(
      `${this.baseUrl}/search?${queryParams.toString()}`
    );
    return response.data;
  }

  /**
   * Get all available game categories
   */
  async getCategories(): Promise<CategoriesResponse> {
    const response = await apiClient.get<CategoriesResponse>(`${this.baseUrl}/categories`);
    return response.data;
  }

  /**
   * Get all game titles/providers
   */
  async getTitles(): Promise<TitlesResponse> {
    const response = await apiClient.get<TitlesResponse>(`${this.baseUrl}/titles`);
    return response.data;
  }

  /**
   * Get game URL to launch the game
   * Requires authentication
   */
  async getGameUrl(gameId: string): Promise<GameUrlResponse> {
    const response = await apiClient.get<GameUrlResponse>(`${this.baseUrl}/${gameId}`);
    return response.data;
  }

  /**
   * Get all game history with optional filters
   */
  async getGameHistory(params?: GameHistoryParams): Promise<any> {
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    if (params?.startDate) queryParams.set('startDate', params.startDate);
    if (params?.endDate) queryParams.set('endDate', params.endDate);
    if (params?.type) queryParams.set('type', params.type);

    const response = await apiClient.get(
      `${this.baseUrl}/history/all?${queryParams.toString()}`
    );
    return response.data;
  }

  /**
   * Launch a game (opens game URL)
   */
  async launchGame(gameId: string): Promise<void> {
    try {
      const { url } = await this.getGameUrl(gameId);
      window.open(url, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('Failed to launch game:', error);
      throw error;
    }
  }
}

export const gameService = new GameService();
