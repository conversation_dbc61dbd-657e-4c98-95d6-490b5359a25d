import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { MenuProvider } from "@/contexts/menu-context"
import { AuthProvider } from "@/components/AuthProvider"
import RootLayoutClient from "@/components/root-layout-client"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "AfricaX1 - Paris Sportifs, Casino et Jeux en Ligne",
  description:
    "Découvrez AfricaX1, votre plateforme de paris sportifs, casino et jeux en ligne. Profitez d'une expérience de jeu exceptionnelle.",
    generator: 'ibetx'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="fr">
      <body className={`${inter.className} bg-black text-white min-h-screen flex flex-col`} suppressHydrationWarning>
        <MenuProvider>
          <AuthProvider>
            <RootLayoutClient>
              {children}
            </RootLayoutClient>
          </AuthProvider>
        </MenuProvider>
      </body>
    </html>
  )
}
