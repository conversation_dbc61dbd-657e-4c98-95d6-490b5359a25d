"use client";

import Image from "next/image";
import { useState } from "react";

interface GameImageProps {
  src: string;
  alt: string;
  fill?: boolean;
  className?: string;
  fallbackSrc?: string;
}

export function GameImage({
  src,
  alt,
  fill = false,
  className = "",
  fallbackSrc = "/placeholder.svg"
}: GameImageProps) {
  const [imgSrc, setImgSrc] = useState(() => {
    // Use proxy for external URLs to avoid CORS issues
    if (src.startsWith('http')) {
      return `/api/proxy-image?url=${encodeURIComponent(src)}`;
    }
    return src;
  });
  const [hasError, setHasError] = useState(false);

  const handleError = () => {
    if (!hasError) {
      setHasError(true);
      // Try original URL first, then fallback
      if (imgSrc.includes('/api/proxy-image')) {
        setImgSrc(src);
      } else {
        setImgSrc(fallbackSrc);
      }
    }
  };

  // For external URLs, we need to handle CORS and loading issues
  const imageProps = {
    src: imgSrc,
    alt,
    className,
    onError: handleError,
    // Add these props to handle external images better
    unoptimized: true, // Always unoptimized for game images
    ...(fill && { fill: true }),
  };

  return fill ? (
    <Image {...imageProps} />
  ) : (
    <Image {...imageProps} width={400} height={300} />
  );
}
